# UQ Analysis Configuration
# This file configures the UQ analysis system for different tasks

# MongoDB Configuration
mongodb:
  host: "localhost"
  port: 27017
  database: "LLM-UQ"
  source_collection: "response_collections"

# Analysis Configuration
analysis:
  # Test mode: only process first N records per task
  test_mode: false
  test_limit: 10
  
  # Batch processing
  batch_size: 100
  
  # Progress reporting
  progress_report_interval: 50

# Tasks to analyze
tasks:
  sentiment_analysis:
    enabled: true
    dataset_sources:
      - "twitter_sentiment"
    output_collection: "UQ_result_sentiment_analysis"
    response_field: "parsed_answer"  # Use parsed_answer for sentiment analysis
    
  explorative_coding:
    enabled: true
    dataset_sources:
      - "pytorch_commits"
    output_collection: "UQ_result_explorative_coding"
    response_field: "raw_answer"  # Use raw_answer for explorative coding

# UQ Methods Configuration
uq_methods:
  # Enable/disable specific methods - All 11 discovered methods
  enabled_methods:
    - "EigValLaplacianJaccardUQ"
    - "EigValLaplacianNLIUQ"
    - "EccentricityJaccardUQ"
    - "EccentricityNLIEntailUQ"
    - "SemanticEntropyNLIUQ"
    - "EmbeddingQwenUQ"
    - "EmbeddingE5UQ"
    - "LUQUQ"
    - "LofreeCPUQ"
    - "NumSetsUQ"
    - "KernelLanguageEntropyUQ"
  
  # Method-specific parameters
  method_params:
    SemanticEntropyNLIUQ:
      entailment_threshold: 0.5
      strict_entailment: true
    EccentricityJaccardUQ:
      thres: 0.9
    EccentricityNLIEntailUQ:
      thres: 0.9
      affinity: "entail"
    EigValLaplacianNLIUQ:
      affinity: "entail"
    LofreeCPUQ:
      lambda1: 1.0
      lambda2: 1.0

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/uq_analysis.log"

# Resume Configuration
resume:
  enabled: true
  check_existing: true
  skip_completed: true
