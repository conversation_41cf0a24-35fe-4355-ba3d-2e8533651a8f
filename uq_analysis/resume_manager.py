#!/usr/bin/env python3
"""
Resume Manager for UQ Analysis
Handles checkpoint and resume functionality to avoid duplicate computations.
"""

import os
import sys
import json
import logging
from typing import Dict, Any, List, Set, Tuple, Optional
from pymongo.collection import Collection
from datetime import datetime, timezone

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

log = logging.getLogger(__name__)


class ResumeManager:
    """Manages resume functionality for UQ analysis."""
    
    def __init__(self, resume_config: Dict[str, Any]):
        """
        Initialize the resume manager.
        
        Args:
            resume_config: Resume configuration
        """
        self.enabled = resume_config.get("enabled", True)
        self.check_existing = resume_config.get("check_existing", True)
        self.skip_completed = resume_config.get("skip_completed", True)
        
        # Cache for completed work
        self.completed_cache: Dict[str, Set[str]] = {}
        
    def create_group_method_key(self, group_key: Dict[str, Any], method_name: str) -> str:
        """
        Create a unique key for a group-method combination.
        
        Args:
            group_key: Group identifier
            method_name: UQ method name
            
        Returns:
            Unique string key
        """
        # Create a stable string representation
        key_parts = [
            str(group_key.get("task_name", "")),
            str(group_key.get("dataset_source", "")),
            str(group_key.get("prompt_seed", "")),
            str(hash(group_key.get("input_text", ""))),  # Hash long input text
            method_name
        ]
        return "|".join(key_parts)
    
    def load_completed_work(self, output_collection: Collection, 
                          task_name: str, methods: List[str]) -> Dict[str, Set[str]]:
        """
        Load information about completed work from the output collection.
        
        Args:
            output_collection: MongoDB collection containing results
            task_name: Name of the task
            methods: List of method names to check
            
        Returns:
            Dictionary mapping collection names to sets of completed keys
        """
        if not self.enabled or not self.check_existing:
            return {}
        
        collection_name = output_collection.name
        
        if collection_name in self.completed_cache:
            return {collection_name: self.completed_cache[collection_name]}
        
        log.info(f"Loading completed work for {collection_name}...")
        
        completed_keys = set()
        
        try:
            # Query for existing results
            query = {
                "group_key.task_name": task_name,
                "method.method_name": {"$in": methods}
            }
            
            projection = {
                "group_key": 1,
                "method.method_name": 1,
                "_id": 0
            }
            
            cursor = output_collection.find(query, projection)
            
            for doc in cursor:
                group_key = doc.get("group_key", {})
                method_name = doc.get("method", {}).get("method_name", "")
                
                if group_key and method_name:
                    key = self.create_group_method_key(group_key, method_name)
                    completed_keys.add(key)
            
            self.completed_cache[collection_name] = completed_keys
            log.info(f"Found {len(completed_keys)} completed work items for {collection_name}")
            
        except Exception as e:
            log.error(f"Failed to load completed work: {str(e)}")
            completed_keys = set()
        
        return {collection_name: completed_keys}
    
    def is_work_completed(self, collection_name: str, group_key: Dict[str, Any], 
                         method_name: str) -> bool:
        """
        Check if work is already completed for a group-method combination.
        
        Args:
            collection_name: Name of the output collection
            group_key: Group identifier
            method_name: UQ method name
            
        Returns:
            True if work is completed, False otherwise
        """
        if not self.enabled or not self.skip_completed:
            return False
        
        if collection_name not in self.completed_cache:
            return False
        
        key = self.create_group_method_key(group_key, method_name)
        return key in self.completed_cache[collection_name]
    
    def filter_pending_work(self, groups: List[Dict[str, Any]], 
                          methods: List[str], collection_name: str) -> List[Tuple[Dict[str, Any], List[str]]]:
        """
        Filter groups and methods to only include pending work.
        
        Args:
            groups: List of group data
            methods: List of method names
            collection_name: Name of the output collection
            
        Returns:
            List of tuples (group, pending_methods)
        """
        if not self.enabled or not self.skip_completed:
            # Return all work if resume is disabled
            return [(group, methods) for group in groups]
        
        pending_work = []
        
        for group in groups:
            group_key = self._extract_group_key(group)
            pending_methods = []
            
            for method_name in methods:
                if not self.is_work_completed(collection_name, group_key, method_name):
                    pending_methods.append(method_name)
            
            if pending_methods:
                pending_work.append((group, pending_methods))
        
        total_original = len(groups) * len(methods)
        total_pending = sum(len(methods) for _, methods in pending_work)
        total_completed = total_original - total_pending
        
        log.info(f"Work summary: {total_completed} completed, {total_pending} pending, "
                f"{total_original} total")
        
        return pending_work
    
    def _extract_group_key(self, group: Dict[str, Any]) -> Dict[str, Any]:
        """Extract group key from group data."""
        group_id = group.get("_id", {})
        return {
            "task_name": group_id.get("task_name"),
            "dataset_source": group_id.get("dataset_source"),
            "prompt_seed": group_id.get("prompt_seed"),
            "input_text": group_id.get("input_text"),
        }
    
    def mark_work_completed(self, collection_name: str, group_key: Dict[str, Any], 
                          method_name: str):
        """
        Mark work as completed in the cache.
        
        Args:
            collection_name: Name of the output collection
            group_key: Group identifier
            method_name: UQ method name
        """
        if not self.enabled:
            return
        
        if collection_name not in self.completed_cache:
            self.completed_cache[collection_name] = set()
        
        key = self.create_group_method_key(group_key, method_name)
        self.completed_cache[collection_name].add(key)
    
    def get_progress_stats(self, collection_name: str) -> Dict[str, int]:
        """
        Get progress statistics.
        
        Args:
            collection_name: Name of the output collection
            
        Returns:
            Dictionary with progress statistics
        """
        completed_count = len(self.completed_cache.get(collection_name, set()))
        
        return {
            "completed": completed_count,
            "cache_enabled": self.enabled,
            "skip_completed": self.skip_completed
        }
    
    def save_checkpoint(self, checkpoint_file: str, data: Dict[str, Any]):
        """
        Save checkpoint data to file.
        
        Args:
            checkpoint_file: Path to checkpoint file
            data: Data to save
        """
        if not self.enabled:
            return
        
        try:
            os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
            
            checkpoint_data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "completed_cache": {k: list(v) for k, v in self.completed_cache.items()},
                "data": data
            }
            
            with open(checkpoint_file, 'w') as f:
                json.dump(checkpoint_data, f, indent=2, default=str)
            
            log.debug(f"Saved checkpoint to {checkpoint_file}")
            
        except Exception as e:
            log.error(f"Failed to save checkpoint: {str(e)}")
    
    def load_checkpoint(self, checkpoint_file: str) -> Optional[Dict[str, Any]]:
        """
        Load checkpoint data from file.
        
        Args:
            checkpoint_file: Path to checkpoint file
            
        Returns:
            Loaded checkpoint data or None
        """
        if not self.enabled or not os.path.exists(checkpoint_file):
            return None
        
        try:
            with open(checkpoint_file, 'r') as f:
                checkpoint_data = json.load(f)
            
            # Restore completed cache
            if "completed_cache" in checkpoint_data:
                self.completed_cache = {
                    k: set(v) for k, v in checkpoint_data["completed_cache"].items()
                }
            
            log.info(f"Loaded checkpoint from {checkpoint_file}")
            return checkpoint_data.get("data", {})
            
        except Exception as e:
            log.error(f"Failed to load checkpoint: {str(e)}")
            return None
