#!/usr/bin/env python3
"""
Data Processing and Analysis Core Logic
Handles reading data from MongoDB, computing UQ, and storing results.
"""

import os
import sys
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from collections import Counter
from pymongo import MongoClient
from pymongo.collection import Collection
from tqdm import tqdm

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from uq_methods.base import BaseUQMethod

log = logging.getLogger(__name__)


class DataProcessor:
    """Handles data processing and UQ computation."""
    
    def __init__(self, mongodb_config: Dict[str, Any]):
        """
        Initialize the data processor.
        
        Args:
            mongodb_config: MongoDB connection configuration
        """
        self.mongodb_config = mongodb_config
        self.client = None
        self.db = None
        self.source_collection = None
        
    def connect_mongodb(self):
        """Connect to MongoDB."""
        try:
            host = self.mongodb_config.get("host", "localhost")
            port = self.mongodb_config.get("port", 27017)
            database = self.mongodb_config.get("database", "LLM-UQ")
            source_collection = self.mongodb_config.get("source_collection", "response_collections")
            
            self.client = MongoClient(f'mongodb://{host}:{port}/')
            self.db = self.client[database]
            self.source_collection = self.db[source_collection]
            
            log.info(f"Connected to MongoDB: {host}:{port}/{database}")
            
        except Exception as e:
            log.error(f"Failed to connect to MongoDB: {str(e)}")
            raise
    
    def disconnect_mongodb(self):
        """Disconnect from MongoDB."""
        if self.client:
            self.client.close()
            log.info("Disconnected from MongoDB")
    
    def get_task_groups(self, task_name: str, dataset_source: str, 
                       limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get groups of responses for a specific task.
        Groups by prompt_seed - each seed should have multiple responses for the same input.
        
        Args:
            task_name: Name of the task
            dataset_source: Source dataset name
            limit: Maximum number of groups to return (for testing)
            
        Returns:
            List of group dictionaries
        """
        pipeline = [
            {
                "$match": {
                    "task_name": task_name,
                    "dataset_source": dataset_source
                }
            },
            {
                "$group": {
                    "_id": {
                        "task_name": "$task_name",
                        "dataset_source": "$dataset_source",
                        "prompt_seed": "$prompt_seed",
                        "input_text": "$input_text"
                    },
                    "count": {"$sum": 1},
                    "docs": {"$push": "$$ROOT"}
                }
            },
            {
                "$match": {
                    "count": {"$gte": 2}  # At least 2 responses for UQ computation
                }
            },
            {
                "$sort": {"_id.prompt_seed": 1}
            }
        ]
        
        if limit:
            pipeline.append({"$limit": limit})
        
        groups = list(self.source_collection.aggregate(pipeline))
        log.info(f"Found {len(groups)} groups for {task_name}/{dataset_source}")
        
        return groups
    
    def extract_responses(self, docs: List[Dict[str, Any]], response_field: str) -> List[str]:
        """
        Extract responses from documents.
        
        Args:
            docs: List of documents
            response_field: Field name to extract responses from
            
        Returns:
            List of response strings
        """
        responses = []
        
        for doc in docs:
            response = doc.get(response_field)
            if response:
                responses.append(str(response))
        
        return responses
    
    def infer_reference_text(self, docs: List[Dict[str, Any]]) -> Optional[str]:
        """
        Infer reference text from documents.
        
        Args:
            docs: List of documents
            
        Returns:
            Most common reference answer or None
        """
        try:
            refs = [d.get("reference_answer") for d in docs if d.get("reference_answer")]
            if refs:
                return Counter(refs).most_common(1)[0][0]
        except Exception:
            pass
        return None
    
    def build_group_key(self, group_id: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build group key from group ID.
        
        Args:
            group_id: Group identifier from aggregation
            
        Returns:
            Standardized group key
        """
        return {
            "task_name": group_id.get("task_name"),
            "dataset_source": group_id.get("dataset_source"),
            "prompt_seed": group_id.get("prompt_seed"),
            "input_text": group_id.get("input_text"),
        }
    
    def compute_uq_for_group(self, group: Dict[str, Any], response_field: str,
                           uq_methods: Dict[str, BaseUQMethod]) -> List[Dict[str, Any]]:
        """
        Compute UQ for a single group using all enabled methods.
        
        Args:
            group: Group data containing documents
            response_field: Field to extract responses from
            uq_methods: Dictionary of UQ method instances
            
        Returns:
            List of UQ result records
        """
        docs = group["docs"]
        group_key = self.build_group_key(group["_id"])
        
        # Extract responses
        responses = self.extract_responses(docs, response_field)
        if len(responses) < 2:
            log.warning(f"Not enough responses for group {group_key}: {len(responses)}")
            return []
        
        # Get reference text
        reference_text = self.infer_reference_text(docs)
        
        results = []
        
        for method_name, method_instance in uq_methods.items():
            try:
                # Set reference text if method supports it
                if hasattr(method_instance, "set_reference_text") and reference_text:
                    method_instance.set_reference_text(reference_text)
                
                # Compute uncertainty
                uq_result = method_instance.compute_uncertainty(responses)
                
                # Extract uncertainty score
                uq_value = (uq_result.get("uncertainty_score") or 
                           uq_result.get("uncertainty") or 
                           uq_result.get("score"))
                
                # Create result record
                record = {
                    "group_key": group_key,
                    "method": {
                        "method_name": method_name,
                        "method_class": method_instance.__class__.__name__,
                        "method_module": method_instance.__class__.__module__,
                    },
                    "outputs": {
                        "uq_value": uq_value,
                        "full_result": uq_result
                    },
                    "metadata": {
                        "n_responses": len(responses),
                        "reference_text": reference_text,
                        "response_field": response_field
                    },
                    "timestamps": {
                        "created_at": datetime.now(timezone.utc)
                    }
                }
                
                results.append(record)
                log.debug(f"Computed UQ for {method_name}: {uq_value}")
                
            except Exception as e:
                log.error(f"Failed to compute UQ for {method_name}: {str(e)}")
                
                # Create error record
                error_record = {
                    "group_key": group_key,
                    "method": {
                        "method_name": method_name,
                        "method_class": method_instance.__class__.__name__,
                        "method_module": method_instance.__class__.__module__,
                    },
                    "outputs": {
                        "uq_value": None,
                        "error": str(e)
                    },
                    "metadata": {
                        "n_responses": len(responses),
                        "reference_text": reference_text,
                        "response_field": response_field
                    },
                    "timestamps": {
                        "created_at": datetime.now(timezone.utc)
                    }
                }
                
                results.append(error_record)
        
        return results
    
    def check_existing_results(self, output_collection: Collection, 
                             group_key: Dict[str, Any], method_name: str) -> bool:
        """
        Check if results already exist for a group and method.
        
        Args:
            output_collection: MongoDB collection to check
            group_key: Group identifier
            method_name: UQ method name
            
        Returns:
            True if results exist, False otherwise
        """
        query = {
            "group_key": group_key,
            "method.method_name": method_name
        }
        
        return output_collection.count_documents(query) > 0
    
    def save_results(self, results: List[Dict[str, Any]], output_collection: Collection):
        """
        Save UQ results to MongoDB.
        
        Args:
            results: List of result records
            output_collection: MongoDB collection to save to
        """
        if not results:
            return
            
        try:
            output_collection.insert_many(results)
            log.debug(f"Saved {len(results)} results to {output_collection.name}")
        except Exception as e:
            log.error(f"Failed to save results: {str(e)}")
            raise
