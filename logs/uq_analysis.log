2025-08-12 21:21:58,347 - __main__ - INFO - Initializing UQ analysis system...
2025-08-12 21:21:59,595 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-08-12 21:21:59,595 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-08-12 21:22:00,099 - datasets - INFO - PyTorch version 2.6.0 available.
2025-08-12 21:22:00,324 - uq_analysis.method_loader - INFO - Discovered 11 UQ methods: ['EigValLaplacianNLIUQ', 'LofreeCPUQ', 'LUQUQ', 'EmbeddingE5UQ', 'SemanticEntropyNLIUQ', 'EigValLaplacianJaccardUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ', 'EmbeddingQwenUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ']
2025-08-12 21:22:00,324 - __main__ - INFO - Discovered 11 UQ methods
2025-08-12 21:22:00,325 - uq_analysis.data_processor - INFO - Connected to MongoDB: localhost:27017/LLM-UQ
2025-08-12 21:22:00,325 - __main__ - INFO - Initialization complete
2025-08-12 21:22:00,325 - uq_analysis.progress_manager - INFO - Starting UQ analysis with 2 tasks
2025-08-12 21:22:00,326 - __main__ - INFO - Processing task: sentiment_analysis
2025-08-12 21:22:02,805 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:02,805 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:08,708 - uq_methods.implementations.LofreeCP - WARNING - Failed to initialize embedding encoder: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:08,877 - uq_analysis.method_loader - INFO - Successfully loaded 11 methods: ['EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ', 'EmbeddingE5UQ', 'LUQUQ', 'LofreeCPUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ']
2025-08-12 21:22:08,877 - __main__ - INFO - Processing sentiment_analysis/twitter_sentiment
2025-08-12 21:22:08,951 - uq_analysis.data_processor - INFO - Found 203 groups for sentiment_analysis/twitter_sentiment
2025-08-12 21:22:08,951 - uq_analysis.resume_manager - INFO - Loading completed work for UQ_result_sentiment_analysis...
2025-08-12 21:22:08,952 - uq_analysis.resume_manager - INFO - Found 0 completed work items for UQ_result_sentiment_analysis
2025-08-12 21:22:08,952 - uq_analysis.resume_manager - INFO - Work summary: 0 completed, 2233 pending, 2233 total
2025-08-12 21:22:08,952 - uq_analysis.progress_manager - INFO - Starting task sentiment_analysis/twitter_sentiment: 203 groups, 11 methods each
2025-08-12 21:22:09,496 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:09,496 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:22:14,799 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:14,833 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:14,834 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:20,427 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:20,907 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=0, input_hash=-4952472): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:20,907 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=0, input_hash=-4952472): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:21,462 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:21,462 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:22:27,464 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:27,500 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:27,501 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:33,503 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:33,966 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=1, input_hash=-8948185): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:33,966 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=1, input_hash=-8948185): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:34,555 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:34,555 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:22:40,441 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:40,479 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:40,479 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:46,455 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:46,915 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=2, input_hash=50795311): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:46,916 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=2, input_hash=50795311): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:47,491 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:47,491 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:22:52,404 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:52,444 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:52,444 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:58,436 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:58,927 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=3, input_hash=87689383): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:58,927 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=3, input_hash=87689383): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:59,470 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:59,470 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
