2025-08-12 21:21:58,347 - __main__ - INFO - Initializing UQ analysis system...
2025-08-12 21:21:59,595 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-08-12 21:21:59,595 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-08-12 21:22:00,099 - datasets - INFO - PyTorch version 2.6.0 available.
2025-08-12 21:22:00,324 - uq_analysis.method_loader - INFO - Discovered 11 UQ methods: ['EigValLaplacianNLIUQ', 'LofreeCPUQ', 'LUQUQ', 'EmbeddingE5UQ', 'SemanticEntropyNLIUQ', 'EigValLaplacianJaccardUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ', 'EmbeddingQwenUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ']
2025-08-12 21:22:00,324 - __main__ - INFO - Discovered 11 UQ methods
2025-08-12 21:22:00,325 - uq_analysis.data_processor - INFO - Connected to MongoDB: localhost:27017/LLM-UQ
2025-08-12 21:22:00,325 - __main__ - INFO - Initialization complete
2025-08-12 21:22:00,325 - uq_analysis.progress_manager - INFO - Starting UQ analysis with 2 tasks
2025-08-12 21:22:00,326 - __main__ - INFO - Processing task: sentiment_analysis
2025-08-12 21:22:02,805 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:02,805 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:08,708 - uq_methods.implementations.LofreeCP - WARNING - Failed to initialize embedding encoder: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:08,877 - uq_analysis.method_loader - INFO - Successfully loaded 11 methods: ['EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ', 'EmbeddingE5UQ', 'LUQUQ', 'LofreeCPUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ']
2025-08-12 21:22:08,877 - __main__ - INFO - Processing sentiment_analysis/twitter_sentiment
2025-08-12 21:22:08,951 - uq_analysis.data_processor - INFO - Found 203 groups for sentiment_analysis/twitter_sentiment
2025-08-12 21:22:08,951 - uq_analysis.resume_manager - INFO - Loading completed work for UQ_result_sentiment_analysis...
2025-08-12 21:22:08,952 - uq_analysis.resume_manager - INFO - Found 0 completed work items for UQ_result_sentiment_analysis
2025-08-12 21:22:08,952 - uq_analysis.resume_manager - INFO - Work summary: 0 completed, 2233 pending, 2233 total
2025-08-12 21:22:08,952 - uq_analysis.progress_manager - INFO - Starting task sentiment_analysis/twitter_sentiment: 203 groups, 11 methods each
2025-08-12 21:22:09,496 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:09,496 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:22:14,799 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:14,833 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:14,834 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:20,427 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:20,907 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=0, input_hash=-4952472): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:20,907 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=0, input_hash=-4952472): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:21,462 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:21,462 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:22:27,464 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:27,500 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:27,501 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:33,503 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:33,966 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=1, input_hash=-8948185): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:33,966 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=1, input_hash=-8948185): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:34,555 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:34,555 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:22:40,441 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:40,479 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:40,479 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:46,455 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:46,915 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=2, input_hash=50795311): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:46,916 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=2, input_hash=50795311): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:47,491 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:47,491 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:22:52,404 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:52,444 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:52,444 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:22:58,436 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:58,927 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=3, input_hash=87689383): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:58,927 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=3, input_hash=87689383): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:22:59,470 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:22:59,470 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:23:04,434 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:23:04,474 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:23:04,474 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:23:10,126 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingE5UQ: E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:23:10,599 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingQwenUQ failed for group (seed=4, input_hash=-6035850): Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:23:10,599 - uq_analysis.progress_manager - ERROR - [sentiment_analysis] EmbeddingE5UQ failed for group (seed=4, input_hash=-6035850): E5 encode failed: CUDA out of memory. Tried to allocate 978.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:23:11,187 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:23:11,187 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:23:16,604 - uq_analysis.data_processor - ERROR - Failed to compute UQ for EmbeddingQwenUQ: Qwen encode failed: CUDA out of memory. Tried to allocate 594.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 521.25 MiB is free. Process 3236 has 352.70 MiB memory in use. Process 2635362 has 14.58 GiB memory in use. Process 2749724 has 6.30 GiB memory in use. Including non-PyTorch memory, this process has 1.87 GiB memory in use. Of the allocated memory 1.51 GiB is allocated by PyTorch, and 14.41 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-08-12 21:23:16,640 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:23:16,641 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:23:18,745 - uq_analysis.data_processor - INFO - Disconnected from MongoDB
2025-08-12 21:42:24,580 - __main__ - INFO - Initializing UQ analysis system...
2025-08-12 21:42:25,759 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-08-12 21:42:25,759 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-08-12 21:42:26,236 - datasets - INFO - PyTorch version 2.6.0 available.
2025-08-12 21:42:26,443 - uq_analysis.method_loader - INFO - Discovered 11 UQ methods: ['EigValLaplacianNLIUQ', 'LofreeCPUQ', 'LUQUQ', 'EmbeddingE5UQ', 'SemanticEntropyNLIUQ', 'EigValLaplacianJaccardUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ', 'EmbeddingQwenUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ']
2025-08-12 21:42:26,444 - __main__ - INFO - Discovered 11 UQ methods
2025-08-12 21:42:26,445 - uq_analysis.data_processor - INFO - Connected to MongoDB: localhost:27017/LLM-UQ
2025-08-12 21:42:26,445 - __main__ - INFO - Initialization complete
2025-08-12 21:42:26,445 - uq_analysis.progress_manager - INFO - Starting UQ analysis with 2 tasks
2025-08-12 21:42:26,445 - __main__ - INFO - Processing task: sentiment_analysis
2025-08-12 21:42:28,913 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:42:28,913 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: intfloat/multilingual-e5-large-instruct
2025-08-12 21:42:35,772 - uq_analysis.method_loader - INFO - Successfully loaded 11 methods: ['EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ', 'EmbeddingE5UQ', 'LUQUQ', 'LofreeCPUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ']
2025-08-12 21:42:35,772 - __main__ - INFO - Processing sentiment_analysis/twitter_sentiment
2025-08-12 21:42:35,819 - uq_analysis.data_processor - INFO - Found 203 groups for sentiment_analysis/twitter_sentiment
2025-08-12 21:42:35,819 - uq_analysis.resume_manager - INFO - Loading completed work for UQ_result_sentiment_analysis...
2025-08-12 21:42:35,819 - uq_analysis.resume_manager - INFO - Found 0 completed work items for UQ_result_sentiment_analysis
2025-08-12 21:42:35,820 - uq_analysis.resume_manager - INFO - Work summary: 0 completed, 2233 pending, 2233 total
2025-08-12 21:42:35,820 - uq_analysis.progress_manager - INFO - Starting task sentiment_analysis/twitter_sentiment: 203 groups, 11 methods each
2025-08-12 21:42:36,177 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-08-12 21:42:36,177 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: Qwen/Qwen3-Embedding-0.6B
2025-08-12 21:42:42,015 - sentence_transformers.SentenceTransformer - INFO - 2 prompts are loaded, with the keys: ['query', 'document']
2025-08-12 21:43:09,041 - uq_analysis.progress_manager - INFO - [PROGRESS] sentiment_analysis/twitter_sentiment: 50/203 groups (24.6%) in 33.2s - Success: 550, Failed: 0, Skipped: 0
2025-08-12 21:43:09,041 - uq_analysis.progress_manager - INFO - [ETA] sentiment_analysis/twitter_sentiment: 101.7s remaining
2025-08-12 21:43:34,258 - uq_analysis.progress_manager - INFO - [PROGRESS] sentiment_analysis/twitter_sentiment: 100/203 groups (49.3%) in 58.4s - Success: 1100, Failed: 0, Skipped: 0
2025-08-12 21:43:34,258 - uq_analysis.progress_manager - INFO - [ETA] sentiment_analysis/twitter_sentiment: 60.2s remaining
2025-08-12 21:44:00,643 - uq_analysis.progress_manager - INFO - [PROGRESS] sentiment_analysis/twitter_sentiment: 150/203 groups (73.9%) in 84.8s - Success: 1650, Failed: 0, Skipped: 0
2025-08-12 21:44:00,643 - uq_analysis.progress_manager - INFO - [ETA] sentiment_analysis/twitter_sentiment: 30.0s remaining
2025-08-12 21:44:27,259 - uq_analysis.progress_manager - INFO - [PROGRESS] sentiment_analysis/twitter_sentiment: 200/203 groups (98.5%) in 111.4s - Success: 2200, Failed: 0, Skipped: 0
2025-08-12 21:44:27,259 - uq_analysis.progress_manager - INFO - [ETA] sentiment_analysis/twitter_sentiment: 1.7s remaining
2025-08-12 21:44:28,302 - uq_analysis.progress_manager - INFO - Finished task sentiment_analysis/twitter_sentiment in 112.48s
2025-08-12 21:44:28,302 - uq_analysis.progress_manager - INFO - [COMPLETED] sentiment_analysis/twitter_sentiment: 203/203 groups (100.0%) in 112.5s - Success: 2233, Failed: 0, Skipped: 0
2025-08-12 21:44:28,311 - __main__ - INFO - Processing task: explorative_coding
2025-08-12 21:44:28,311 - uq_analysis.method_loader - INFO - Successfully loaded 11 methods: ['EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ', 'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ', 'EmbeddingE5UQ', 'LUQUQ', 'LofreeCPUQ', 'NumSetsUQ', 'KernelLanguageEntropyUQ']
2025-08-12 21:44:28,311 - __main__ - INFO - Processing explorative_coding/pytorch_commits
2025-08-12 21:44:28,546 - uq_analysis.data_processor - INFO - Found 197 groups for explorative_coding/pytorch_commits
2025-08-12 21:44:28,546 - uq_analysis.resume_manager - INFO - Loading completed work for UQ_result_explorative_coding...
2025-08-12 21:44:28,546 - uq_analysis.resume_manager - INFO - Found 0 completed work items for UQ_result_explorative_coding
2025-08-12 21:44:28,547 - uq_analysis.resume_manager - INFO - Work summary: 0 completed, 2167 pending, 2167 total
2025-08-12 21:44:28,547 - uq_analysis.progress_manager - INFO - Starting task explorative_coding/pytorch_commits: 197 groups, 11 methods each
2025-08-12 23:47:45,810 - uq_analysis.progress_manager - INFO - [PROGRESS] explorative_coding/pytorch_commits: 50/197 groups (25.4%) in 7397.3s - Success: 550, Failed: 0, Skipped: 0
2025-08-12 23:47:45,810 - uq_analysis.progress_manager - INFO - [ETA] explorative_coding/pytorch_commits: 21748.0s remaining
2025-08-13 02:41:56,248 - uq_analysis.progress_manager - INFO - [PROGRESS] explorative_coding/pytorch_commits: 100/197 groups (50.8%) in 17847.7s - Success: 1100, Failed: 0, Skipped: 0
2025-08-13 02:41:56,248 - uq_analysis.progress_manager - INFO - [ETA] explorative_coding/pytorch_commits: 17312.3s remaining
2025-08-13 05:29:35,848 - uq_analysis.progress_manager - INFO - [PROGRESS] explorative_coding/pytorch_commits: 150/197 groups (76.1%) in 27907.3s - Success: 1650, Failed: 0, Skipped: 0
2025-08-13 05:29:35,848 - uq_analysis.progress_manager - INFO - [ETA] explorative_coding/pytorch_commits: 8744.3s remaining
2025-08-13 07:54:01,107 - uq_analysis.progress_manager - INFO - Finished task explorative_coding/pytorch_commits in 36572.56s
2025-08-13 07:54:01,107 - uq_analysis.progress_manager - INFO - [COMPLETED] explorative_coding/pytorch_commits: 197/197 groups (100.0%) in 36572.6s - Success: 2167, Failed: 0, Skipped: 0
2025-08-13 07:54:01,119 - uq_analysis.progress_manager - INFO - Analysis completed in 36694.67s
2025-08-13 07:54:01,119 - uq_analysis.progress_manager - INFO - [FINAL SUMMARY] Groups: 400/400, Methods: 4400/22, Success: 4400, Failed: 0, Skipped: 0, Time: 36694.7s
2025-08-13 07:54:01,120 - __main__ - INFO - UQ analysis completed successfully
2025-08-13 07:54:01,120 - uq_analysis.data_processor - INFO - Disconnected from MongoDB
